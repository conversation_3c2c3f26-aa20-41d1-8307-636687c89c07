import { Injectable, Logger, Inject, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AutoActionHandler } from '../shared/auto-action.service';
import { FactoryService } from './factory.service';
import { WorkSession } from '../work-session/entity/work-session.entity';
import { Factory } from './entity/factory.entity';
import { User } from '../user/entity/user.entity';

interface WorkAutoSetting {
  userId: number;
  targetId: string; // Factory ID
}

@Injectable()
export class WorkAutoHandler implements AutoActionHandler {
  private readonly logger = new Logger(WorkAutoHandler.name);
  private autoSettings = new Map<string, WorkAutoSetting>();

  constructor(
    @Inject(forwardRef(() => FactoryService))
    private factoryService: FactoryService,
    @InjectRepository(Factory)
    private factoryRepository: Repository<Factory>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(WorkSession)
    private workSessionRepository: Repository<WorkSession>,
  ) {}

  /**
   * Execute the auto work action
   * @param userId User ID
   * @param factoryId Factory ID
   * @param energyAmount Amount of energy to spend
   */
  async executeAction(
    userId: number,
    factoryId: string,
    energyAmount: number,
  ): Promise<void> {
    try {
      this.logger.log(
        `Executing auto work for user ${userId} at factory ${factoryId} with ${energyAmount} energy`,
      );

      // Skip if no energy to spend
      if (energyAmount <= 0) {
        this.logger.log(`Skipping auto work - no energy available`);
        return;
      }

      // Work at the factory
      const result = await this.factoryService.workAtFactory(+factoryId, userId, {
        energySpent: energyAmount,
      });

      this.logger.log(
        `Auto work successful for user ${userId} at factory ${factoryId}. Energy consumed: ${energyAmount}, Remaining energy: ${result.user.energy}`,
      );
    } catch (error) {
      this.logger.error(
        `Error executing auto work for user ${userId} at factory ${factoryId}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Check if the factory is still valid for auto work
   * @param userId User ID
   * @param factoryId Factory ID
   */
  async checkActionValidity(
    userId: number,
    factoryId: string,
  ): Promise<boolean> {
    try {
      // Check if factory exists
      const factory = await this.factoryRepository.findOne({
        where: { id: +factoryId },
      });

      if (!factory) {
        this.logger.log(`Auto work validity check failed - Factory ${factoryId} no longer exists`);
        return false;
      }

      // Check if user is premium (required for auto mode)
      const user = await this.userRepository.findOne({
        where: { id: userId },
      });

      if (!user || !user.isPremium) {
        this.logger.log(
          `Auto work validity check failed - User ${userId} is no longer premium or does not exist`,
        );
        return false;
      }

      // Check if factory has reached max workers
      const currentWorkerCount = await this.factoryService.getWorkerCount(+factoryId);
      if (currentWorkerCount >= factory.maxWorkers) {
        this.logger.log(
          `Auto work validity check failed - Factory ${factoryId} has reached maximum capacity (${currentWorkerCount}/${factory.maxWorkers} workers)`,
        );
        return false;
      }

      // All checks passed - factory is valid for auto work
      this.logger.log(
        `Auto work validity check passed - Factory ${factoryId} is valid. Workers: ${currentWorkerCount}/${factory.maxWorkers}, User ${userId} premium: ${user.isPremium}`
      );

      return true;
    } catch (error) {
      this.logger.error(
        `Error checking auto work validity for user ${userId} at factory ${factoryId}: ${error.message}`,
      );
      // Return false for errors to be safe and prevent invalid operations
      return false;
    }
  }

  /**
   * Save auto work settings
   * @param userId User ID
   * @param factoryId Factory ID
   */
  async saveAutoSettings(
    userId: number,
    factoryId: string,
  ): Promise<void> {
    const key = `${userId}_${factoryId}`;
    this.autoSettings.set(key, { userId, targetId: factoryId });
    this.logger.log(
      `Saved auto work settings for user ${userId} at factory ${factoryId}`,
    );
  }

  /**
   * Remove auto work settings
   * @param userId User ID
   * @param factoryId Factory ID
   */
  async removeAutoSettings(
    userId: number,
    factoryId: string,
  ): Promise<void> {
    const key = `${userId}_${factoryId}`;
    this.autoSettings.delete(key);
    this.logger.log(
      `Removed auto work settings for user ${userId} at factory ${factoryId}`,
    );
  }

  /**
   * Get all active auto work settings
   * This method queries the database to find users with active work auto mode
   * This is critical for server restart scenarios
   */
  async getActiveAutoSettings(): Promise<{ userId: number; targetId: string }[]> {
    try {
      // Query users who have active work auto mode
      const usersWithAutoWork = await this.userRepository.find({
        where: {
          activeAutoMode: 'work' as any, // Cast to avoid enum issues
        },
        select: ['id', 'autoTargetId'],
      });

      const activeSettings = usersWithAutoWork
        .filter(user => user.autoTargetId) // Only include users with a target ID
        .map(user => ({
          userId: user.id,
          targetId: user.autoTargetId,
        }));

      this.logger.log(`Found ${activeSettings.length} active work auto settings in database`);
      return activeSettings;
    } catch (error) {
      this.logger.error(`Error fetching active work auto settings: ${error.message}`);
      // Fallback to in-memory settings if database query fails
      return Array.from(this.autoSettings.values());
    }
  }
}


