import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  Index,
} from 'typeorm';
import { User } from '../../user/entity/user.entity';
import { Chat } from './chat.entity';
import type { MessageReadStatus } from './message-read-status.entity';

export enum MessageType {
  TEXT = 'text',
  SYSTEM = 'system',
}

export enum MessageStatus {
  SENT = 'sent',
  DELIVERED = 'delivered',
  READ = 'read',
}

@Entity()
@Index(['chat', 'createdAt'])
@Index(['sender', 'createdAt'])
export class Message {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => Chat, (chat) => chat.messages, { onDelete: 'CASCADE' })
  chat: Chat;

  @ManyToOne(() => User)
  sender: User;

  @Column('text')
  content: string;

  @Column({
    type: 'enum',
    enum: MessageType,
    default: MessageType.TEXT,
  })
  type: MessageType;

  @Column({
    type: 'enum',
    enum: MessageStatus,
    default: MessageStatus.SENT,
  })
  status: MessageStatus;

  @OneToMany('MessageReadStatus', (readStatus: any) => readStatus.message, {
    cascade: true,
  })
  readStatuses: MessageReadStatus[];

  @Column({ type: 'timestamp', nullable: true })
  editedAt: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Virtual property to check if message is read by specific user
  isReadBy(userId: number): boolean {
    // If the user is the sender, consider it read by default
    if (this.sender && this.sender.id === userId) {
      return true;
    }

    // Otherwise, check if there's a read status for this user
    return this.readStatuses?.some(
      (status) => status.user.id === userId && status.readAt !== null,
    ) || false;
  }

  // Virtual property to get read status for specific user
  getReadStatusFor(userId: number): MessageReadStatus | undefined {
    return this.readStatuses?.find((status) => status.user.id === userId);
  }
}
