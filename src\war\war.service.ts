import {
  Injectable,
  BadRequestException,
  NotFoundException,
  OnApplicationBootstrap,
  Inject,
  forwardRef,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Not, Repository } from 'typeorm';
import { War, WarType, WarStatus, WarTarget } from './entity/war.entity';
import { Region, RegionStatus } from '../region/entity/region.entity';
import { User } from '../user/entity/user.entity';
import { CreateWarDto } from './dto/create-war.dto';
import { ParticipateInWarDto } from './dto/participate-in-war.dto';
import { NotificationService } from '../notification/notification.service';
import { NotificationType } from '../notification/entity/notification.entity';
import { SchedulerRegistry } from '@nestjs/schedule';
import { WarAutoService } from './war-auto.service';
import { UserService } from 'src/user/user.service';
import { State } from '../state/entity/state.entity';
import { EnergyService } from 'src/user/energy.service';
import { StateService } from 'src/state/state.service';

@Injectable()
export class WarService implements OnApplicationBootstrap {
  private readonly WAR_DURATION_MS = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

  constructor(
    @InjectRepository(War)
    private warRepository: Repository<War>,
    @InjectRepository(Region)
    private regionRepository: Repository<Region>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private notificationService: NotificationService,
    @Inject(forwardRef(() => WarAutoService))
    private warAutoService: WarAutoService,
    private schedulerRegistry: SchedulerRegistry,
    private userService: UserService,
    @Inject(forwardRef(() => EnergyService))
    private energyService: EnergyService,
    private stateService: StateService,
  ) {}

  async findAllWars(): Promise<War[]> {
    return this.warRepository.find({
      relations: {
        attackerRegion: true,
        defenderRegion: true,
        declaredBy: true,
      },
      order: {
        declaredAt: 'DESC', // Add this to sort in descending order
      },
    });
  }

  async findActiveWars(): Promise<War[]> {
    return this.warRepository.find({
      where: [
        { status: WarStatus.PENDING },
        { status: WarStatus.GROUND_PHASE },
      ],
      relations: {
        attackerRegion: true,
        defenderRegion: true,
        declaredBy: true,
      },
    });
  }

  async findWarById(id: string): Promise<War> {
    const war = await this.warRepository.findOne({
      where: { id },
      relations: {
        attackerRegion: true,
        defenderRegion: true,
        declaredBy: true,
        targetRegion: true,
      },
    });

    if (!war) {
      throw new NotFoundException(`War with ID ${id} not found`);
    }

    return war;
  }

  async declareWar(userId: number, createWarDto: CreateWarDto): Promise<War> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['region', 'region.state'],
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const [attackerRegion, defenderRegion] = await Promise.all([
      this.regionRepository.findOneBy({ id: createWarDto.attackerRegionId }),
      this.regionRepository.findOneBy({ id: createWarDto.defenderRegionId }),
    ]);

    if (!attackerRegion || !defenderRegion) {
      throw new NotFoundException('One or both regions not found');
    }

    // Validate if the user has authority to declare war
    const hasAuthority = await this.validateWarAuthority(user);
    if (!hasAuthority) {
      throw new BadRequestException('You do not have authority to declare war');
    }

    // For ground wars, validate that regions are bordering
    if (createWarDto.warType === WarType.GROUND) {
      const regionsAreBordering = await this.validateBorderingRegions(
        attackerRegion,
        defenderRegion,
      );

      if (!regionsAreBordering) {
        throw new BadRequestException(
          'Cannot declare ground war on a non-bordering region',
        );
      }
    }

    // Determine the damage requirement based on region strength
    const damageRequirement = await this.calculateDamageRequirement(
      createWarDto.defenderRegionId,
    );

    const existingWar = await this.warRepository.findOne({
      where: {
        attackerRegion: { id: attackerRegion.id },
        defenderRegion: { id: defenderRegion.id },
        status: Not(WarStatus.ENDED),
      },
    });

    if (existingWar) {
      throw new BadRequestException(
        'An active war already exists between these regions',
      );
    }

    // Create the war entity
    const newWar = this.warRepository.create({
      warType: createWarDto.warType,
      warTarget: createWarDto.warTarget,
      status: WarStatus.GROUND_PHASE,
      declaration: createWarDto.declaration,
      attackerRegion,
      defenderRegion,
      declaredBy: user,
      declaredAt: new Date(),
      startedAt: new Date(),
      damageRequirement,
      attackerGroundDamage: 0,
      defenderGroundDamage: 0,
      participants: {
        attackers: [],
        defenders: [],
      },
      battleEvents: [],
    });

    const savedWar = await this.warRepository.save(newWar);

    // Send notifications about war declaration
    await this.sendWarDeclarationNotifications(savedWar);

    // Schedule war end exactly 24 hours from now
    const timeout = global.setTimeout(async () => {
      await this.endWar(savedWar.id);
    }, this.WAR_DURATION_MS);

    // Store the timeout reference
    this.schedulerRegistry.addTimeout(`war_end_${savedWar.id}`, timeout);

    return savedWar;
  }

  private async validateWarAuthority(user: User): Promise<boolean> {
    if (!user.region) {
      return false; // User is not part of a region
    }

    if (!user.region || !user.region.state) {
      return false; // User is not part of a region or region has no state
    }

    const state = await this.stateService.findStateLeader(user.id);

    if (!state) {
      return false; // State not found
    }

    return state.leader.id === user.id; // Check if user is the state leader
  }

  private async validateBorderingRegions(
    attackerRegion: Region,
    defenderRegion: Region,
  ): Promise<boolean> {
    if (!attackerRegion || !attackerRegion.bordersWith) {
      return false;
    }

    return await attackerRegion.bordersWith.includes(
      defenderRegion.countryCode,
    );
  }

  private async calculateDamageRequirement(
    defenderRegionId: string,
  ): Promise<number> {
    const defenderRegion = await this.regionRepository.findOneBy({
      id: defenderRegionId,
    });

    if (!defenderRegion) {
      return 10000; // Default value
    }

    // Basic calculation for MVP
    return (defenderRegion.developmentIndex || 1) * 1000;
  }

  private async sendWarDeclarationNotifications(war: War): Promise<void> {
    try {
      const attackerName = war.attackerRegion?.name || 'Unknown';
      const defenderName = war.defenderRegion?.name || 'Unknown';
      const title = 'War Declared!';
      const content = `${attackerName} has declared war on ${defenderName}. War type: ${war.warType}`;

      // Get affected users (simplified for MVP)
      const affectedUsers = await this.userRepository.find({
        where: [
          { region: { id: war.attackerRegion.id } },
          { region: { id: war.defenderRegion.id } },
        ],
      });

      // await this.notificationService.createWarNotification({
      //   users: affectedUsers,
      //   type: NotificationType.WAR_DECLARED,
      //   title,
      //   content,
      //   referenceId: war.id,
      //   isUrgent: true
      // });
    } catch (error) {
      console.error('Failed to send war declaration notifications:', error);
    }
  }

  // Add this helper method to centralize the winner determination logic
  public determineWarWinner(war: War): 'attacker' | 'defender' | null {
    if (war.status !== WarStatus.ENDED) {
      return null;
    }

    return war.attackerGroundDamage >= war.damageRequirement &&
      war.attackerGroundDamage > war.defenderGroundDamage
      ? 'attacker'
      : 'defender';
  }

  async findWarsByUser(userId: number): Promise<War[]> {
    const wars = await this.warRepository.find({
      relations: {
        attackerRegion: true,
        defenderRegion: true,
        declaredBy: true,
      },
      order: {
        declaredAt: 'DESC', // Add this to sort in descending order
      },
    });

    // Filter wars where user is either a participant or the declarer
    return wars.filter((war) => {
      // Check if user is the declarer
      if (war.declaredBy?.id === userId) {
        return true;
      }

      // Check if user is a participant
      const isAttacker = war.participants?.attackers?.some(
        (participant) => participant.userId === userId,
      );
      const isDefender = war.participants?.defenders?.some(
        (participant) => participant.userId === userId,
      );

      return isAttacker || isDefender;
    });
  }

  async participateInWar(
    userId: number,
    warId: string,
    participateDto: ParticipateInWarDto,
  ): Promise<War> {
    // Get user without updating energy (to avoid double energy update)
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['region', 'region.state'],
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (user.isTraveling) {
      throw new BadRequestException(
        'You are already traveling, cannot participate in war!',
      );
    }

    // Get the war
    const war = await this.findWarById(warId);
    if (!war) {
      throw new NotFoundException(`War with ID ${warId} not found`);
    }

    if (war.status !== WarStatus.GROUND_PHASE) {
      throw new BadRequestException(
        'Can only participate in wars during the ground phase',
      );
    }

    // Determine which side the user is on based on their region
    const isAttacker = user.region?.id === war.attackerRegion.id;
    const isDefender = user.region?.id === war.defenderRegion.id;

    if (!isAttacker && !isDefender) {
      throw new BadRequestException(
        'You can only participate in wars involving your region',
      );
    }

    // Calculate current energy without saving (to avoid double energy update)
    const currentEnergy = this.energyService.calculateCurrentEnergy(user);
    let energyToSpend = currentEnergy;

    // Handle auto mode
    if (participateDto.autoMode) {
      // Only premium users can use auto mode for initial setup
      if (!user.isPremium && energyToSpend === 0) {
        throw new BadRequestException(
          'Auto mode is only available for premium users',
        );
      }

      // Start auto attack - this will handle the initial attack and set up the cron job
      await this.warAutoService.startAutoAttack(
        userId,
        warId,
        participateDto.autoEnergyPercentage || 50, // Default to 50% if not specified
      );

      // Return the war without executing the manual attack logic
      // The auto attack system will handle the initial attack
      return this.findWarById(warId);
    }

    // Log the user's current energy
    console.log(
      `War participation - User ID: ${userId}, Current energy: ${currentEnergy}, Requested energy: ${energyToSpend}`,
    );

    // Ensure we don't try to use more energy than available
    const originalEnergyRequest = energyToSpend;
    energyToSpend = Math.min(energyToSpend, currentEnergy);

    // If energy was capped, log it
    if (energyToSpend < originalEnergyRequest) {
      console.log(
        `Energy request capped - Original: ${originalEnergyRequest}, Available: ${energyToSpend}`,
      );
    }

    // If no energy available, skip this attack
    if (energyToSpend <= 0) {
      throw new BadRequestException(
        `Insufficient energy to participate in war. Available: ${currentEnergy}, Required: ${originalEnergyRequest}`,
      );
    }

    // Log the final energy to spend
    console.log(
      `Final energy to spend - User ID: ${userId}, Energy: ${energyToSpend}`,
    );

    // Use a transaction to handle energy consumption and user updates atomically
    const updatedUser = await this.userRepository.manager.transaction(async (transactionManager) => {
      // Get user with pessimistic locking
      const lockedUser = await transactionManager.findOne(User, {
        where: { id: userId },
        lock: { mode: 'pessimistic_write' }
      });

      if (!lockedUser) {
        throw new NotFoundException('User not found');
      }

      // Update energy within the transaction
      lockedUser.updateEnergy();

      // Check if user has enough energy
      if (lockedUser.energy < energyToSpend) {
        throw new BadRequestException(`Insufficient energy: User has ${lockedUser.energy} but needs ${energyToSpend}`);
      }

      // Log energy consumption for debugging
      console.log(
        `Before consumption - User ID: ${userId}, Energy: ${lockedUser.energy}, Amount to consume: ${energyToSpend}`,
      );

      // Consume energy directly
      lockedUser.energy -= energyToSpend;

      // Log energy after consumption
      console.log(
        `After consumption - User ID: ${userId}, Remaining energy: ${lockedUser.energy}`,
      );

      // Grant XP for fighting (2 XP per energy spent)
      lockedUser.experience += energyToSpend * 2;
      lockedUser.checkAndUpdateLevel();

      // Save and return the updated user
      return await transactionManager.save(User, lockedUser);
    });

    // Calculate damage based on energy spent and user attributes
    const damage = this.calculateDamage(energyToSpend, updatedUser);

    // Update war with new damage
    if (isAttacker) {
      war.attackerGroundDamage += damage;
      if (!war.participants.attackers) {
        war.participants.attackers = [];
      }
      war.participants.attackers.push({
        userId,
        username: user.username,
        damage,
        energySpent: energyToSpend, // Use actual energy spent
      });
    } else {
      war.defenderGroundDamage += damage;
      if (!war.participants.defenders) {
        war.participants.defenders = [];
      }
      war.participants.defenders.push({
        userId,
        username: user.username,
        damage,
        energySpent: energyToSpend, // Use actual energy spent
      });
    }

    // Save and return updated war
    return this.warRepository.save(war);
  }

  private calculateDamage(energyAmount: number, user: User): number {
    // Enhanced damage calculation including user attributes
    // Based on the formula: Damage = (1 + Strength/100 + Level/200) * Energy * 100

    // Extract user attributes
    const strength = user.strength || 10; // Default to 10 if not set
    const level = user.level || 1; // Default to 1 if not set

    // Calculate the multiplier
    const multiplier = 1 + strength / 100 + level / 200;

    // Calculate and return the damage
    return Math.floor(energyAmount * 100 * multiplier);
  }

  /**
   * Stop auto attack mode for a user in a specific war
   */
  async stopAutoAttack(userId: number, warId: string): Promise<void> {
    // Verify the war exists
    const war = await this.findWarById(warId);
    if (!war) {
      throw new NotFoundException(`War with ID ${warId} not found`);
    }

    // Verify the user exists
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['region'],
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Check if user has active auto mode for this war
    if (user.activeAutoMode !== 'war' || user.autoTargetId !== warId) {
      throw new BadRequestException('No active auto attack found for this war');
    }

    // Stop the auto attack
    await this.warAutoService.stopAutoAttack(userId, warId);
  }

  async endWar(warId: string): Promise<War> {
    const war = await this.findWarById(warId);

    if (war.status === WarStatus.ENDED) {
      throw new BadRequestException('War is already ended');
    }

    // Update war status to ENDED
    war.status = WarStatus.ENDED;
    war.endedAt = new Date();

    // Stop auto attacks for all participants
    const allParticipants = [
      ...(war.participants?.attackers || []),
      ...(war.participants?.defenders || []),
    ];

    // Stop auto attacks for each participant
    for (const participant of allParticipants) {
      this.warAutoService.stopAutoAttack(participant.userId, warId);
    }

    // Clean up the timeout
    try {
      this.schedulerRegistry.deleteTimeout(`war_end_${warId}`);
    } catch (error) {
      console.log(`[endWar] Error clearing timeout: ${error.message}`);
    }

    // Save the war with updated status
    const savedWar = await this.warRepository.save(war);

    // Process war outcome
    await this.processWarOutcome(savedWar);

    return savedWar;
  }

  /**
   * Process the outcome of a war, including region transfers for conquest wars
   */
  private async processWarOutcome(war: War): Promise<void> {
    // Determine the winner
    const winner = this.determineWarWinner(war);
    if (!winner) {
      return;
    }
    // For conquest wars, use defenderRegion as targetRegion if targetRegion is not set
    if (
      war.warTarget === WarTarget.CONQUEST &&
      !war.targetRegion &&
      war.defenderRegion
    ) {
      console.log('[processWarOutcome] Setting defenderRegion as targetRegion');
      war.targetRegion = war.defenderRegion;
    }

    if (
      winner === 'attacker' &&
      war.warTarget === WarTarget.CONQUEST &&
      war.targetRegion
    ) {
      await this.transferRegion(war);
    }

    // Send notifications about the war outcome
    await this.sendWarEndNotifications(war, winner);
  }

  /**
   * Transfer a region from one state to another after a successful conquest
   */
  private async transferRegion(war: War): Promise<void> {
    try {
      // Load the full region with state relation
      const targetRegion = await this.regionRepository.findOne({
        where: { id: war.targetRegion.id },
        relations: ['state'],
      });

      if (!targetRegion) {
        console.error(
          `[transferRegion] Target region with ID ${war.targetRegion.id} not found`,
        );
        return;
      }

      // Load the attacker state
      const attackerRegion = await this.regionRepository.findOne({
        where: { id: war.attackerRegion.id },
        relations: ['state'],
      });

      if (!attackerRegion?.state) {
        return;
      }
      console.log(
        `[transferRegion] Attacker state found: ${attackerRegion.state.name}`,
      );

      // If the region belongs to a state, remove it from that state
      if (targetRegion.state) {
        const defenderStateId = targetRegion.state.id;
        const defenderStateName = targetRegion.state.name;
        console.log(
          `[transferRegion] Removing region from current state: ${defenderStateId}`,
        );

        // Check if this is the last region in the state
        const defenderState = await this.stateService.findById(defenderStateId);

        const isLastRegion = defenderState?.regions?.length === 1;

        // First, remove the region from the state
        try {
          await this.stateService.removeRegion(
            defenderStateId,
            targetRegion.id,
          );

          // If this was the last region, delete the state
          if (isLastRegion && defenderState) {
            try {
              await this.stateService.removeState(defenderStateId);

              // Notify the state leader
              if (defenderState.leader) {
                await this.notificationService.createNotification(
                  defenderState.leader,
                  NotificationType.REGION_CONQUERED,
                  'State Dissolved',
                  `Your state ${defenderStateName} has been dissolved as it lost its last region.`,
                  war.id,
                  'war',
                  true,
                );
              }
            } catch (error) {
              console.error(
                `[transferRegion] Error removing state: ${error.message}`,
              );
            }
          }
        } catch (error) {
          console.error(
            `[transferRegion] Error removing region using stateService: ${error.message}`,
          );

          // Fallback: manually update the region
          targetRegion.state = null;
          targetRegion.status = RegionStatus.INDEPENDENT;
          await this.regionRepository.save(targetRegion);

          // If this was the last region, delete the state
          if (isLastRegion) {
            try {
              await this.stateService.removeState(defenderStateId);
            } catch (error) {
              console.error(
                `[transferRegion] Error deleting state manually: ${error.message}`,
              );
            }
          }
        }

        // Verify the region is now independent
        const updatedRegion = await this.regionRepository.findOne({
          where: { id: targetRegion.id },
          relations: ['state'],
        });
      }

      await this.stateService.addRegion(attackerRegion.state.id, {
        regionId: targetRegion.id,
      });

      // Notify users in the conquered region
      const usersInRegion = await this.userRepository.find({
        where: { region: { id: targetRegion.id } },
      });

      const attackerStateName = attackerRegion.state.name || 'Unknown';
      const regionName = targetRegion.name || 'Unknown';

      for (const user of usersInRegion) {
        await this.notificationService.createNotification(
          user,
          NotificationType.REGION_CONQUERED,
          'Region Conquered',
          `Your region ${regionName} has been conquered by ${attackerStateName}.`,
          war.id,
          'war',
          true,
        );
      }
    } catch (error) {
      console.error('Error transferring region:', error);
    }
  }

  /**
   * Send notifications about the end of a war
   */
  private async sendWarEndNotifications(
    war: War,
    winner: 'attacker' | 'defender',
  ): Promise<void> {
    try {
      const attackerName =
        war.attackerState?.name || war.attackerRegion?.name || 'Unknown';
      const defenderName =
        war.defenderState?.name || war.defenderRegion?.name || 'Unknown';

      const title = 'War Ended';
      let content = '';

      if (winner === 'attacker') {
        content = `${attackerName} has emerged victorious in the war against ${defenderName}.`;
        if (war.warTarget === WarTarget.CONQUEST && war.targetRegion) {
          content += ` ${war.targetRegion.name} has been conquered.`;
        }
      } else {
        content = `${defenderName} has successfully defended against ${attackerName}'s attack.`;
      }

      // Get affected users
      const affectedUsers = await this.userRepository.find({
        where: [
          { region: { id: war.attackerRegion.id } },
          { region: { id: war.defenderRegion.id } },
        ],
      });

      // Add users from the target region if it's different from attacker/defender regions
      if (
        war.targetRegion &&
        war.targetRegion.id !== war.attackerRegion.id &&
        war.targetRegion.id !== war.defenderRegion.id
      ) {
        const targetRegionUsers = await this.userRepository.find({
          where: { region: { id: war.targetRegion.id } },
        });
        affectedUsers.push(...targetRegionUsers);
      }

      // Send notifications to all affected users
      for (const user of affectedUsers) {
        await this.notificationService.createNotification(
          user,
          NotificationType.WAR_ENDED,
          title,
          content,
          war.id,
          'war',
          true, // Send email for important notifications
        );
      }
    } catch (error) {
      console.error('Failed to send war end notifications:', error);
    }
  }

  async onApplicationBootstrap() {
    await this.restoreWarEndSchedules();
  }

  async restoreWarEndSchedules() {
    const activeWars = await this.warRepository.find({
      where: [
        { status: WarStatus.PENDING },
        { status: WarStatus.GROUND_PHASE },
      ],
    });

    for (const war of activeWars) {
      if (!war.startedAt) continue;

      const now = new Date().getTime();
      const warEndTime = war.startedAt.getTime() + this.WAR_DURATION_MS;
      const remainingTime = warEndTime - now;

      if (remainingTime <= 0) {
        // War should have ended already
        await this.endWar(war.id);
      } else {
        // Reschedule the remaining time
        const timeout = global.setTimeout(async () => {
          await this.endWar(war.id);
        }, remainingTime);

        this.schedulerRegistry.addTimeout(`war_end_${war.id}`, timeout);
      }
    }
  }
}
