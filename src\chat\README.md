# Real-Time Chat System - Frontend Integration Guide

A comprehensive real-time chat system built with NestJS, Socket.IO, and TypeORM. This guide provides everything a frontend developer needs to integrate with the chat system.

## 🚀 Quick Start

### Prerequisites
- JWT authentication token from the `/auth/login` endpoint
- Socket.IO client library (`socket.io-client`)
- HTTP client for REST API calls

### Basic Setup
```javascript
import io from 'socket.io-client';

// Initialize chat connection
const token = 'your-jwt-token';
const socket = io('/chat', {
  auth: { token }
});

// The user is automatically joined to all their chat rooms on connection
```

## 📋 Features

- **Real-time messaging** using WebSocket connections with automatic reconnection
- **Direct and group chats** with participant management
- **Message persistence** with read status tracking
- **Optimized cursor-based pagination** for efficient message loading
- **Rate limiting** to prevent spam (10 messages/minute WebSocket, 30/minute HTTP)
- **Input sanitization** for security using DOMPurify
- **Typing indicators** and **read receipts**
- **Connection health monitoring** with ping/pong heartbeat
- **Memory leak prevention** with automatic cleanup
- **Performance optimizations** for large group chats
- **Database indexing** for fast queries
- **Authentication integration** with JWT system

## 🔗 HTTP API Endpoints

All HTTP endpoints require JWT authentication via `Authorization: Bearer <token>` header.

### Chat Management

#### Create Chat
```http
POST /api/chats
Content-Type: application/json
Authorization: Bearer <token>

{
  "type": "direct",           // "direct" or "group"
  "participantIds": [2],      // Array of user IDs (excluding yourself)
  "name": "Chat Name",        // Required for group chats
  "description": "Optional"   // Optional description
}
```

**Response:**
```json
{
  "id": "uuid",
  "type": "direct",
  "name": null,
  "description": null,
  "participants": [
    {
      "id": 1,
      "username": "user1",
      "level": 5
    }
  ],
  "unreadCount": 0,
  "createdAt": "2024-01-15T10:30:00Z",
  "updatedAt": "2024-01-15T10:30:00Z"
}
```

#### Get User Chats (with pagination)
```http
GET /api/chats?limit=20&cursor=uuid
Authorization: Bearer <token>
```

**Query Parameters:**
- `limit` (optional): Number of chats to return (1-100, default: 20)
- `cursor` (optional): UUID for pagination (last chat ID from previous request)

**Response:**
```json
{
  "chats": [
    {
      "id": "uuid",
      "type": "direct",
      "name": null,
      "participants": [...],
      "lastMessage": {
        "id": "uuid",
        "content": "Hello!",
        "type": "text",
        "sender": {...},
        "isRead": false,
        "createdAt": "2024-01-15T10:30:00Z"
      },
      "unreadCount": 3,
      "lastMessageAt": "2024-01-15T10:30:00Z"
    }
  ],
  "hasMore": true,
  "nextCursor": "uuid"
}
```

#### Get Chat Messages (with pagination)
```http
GET /api/chats/:chatId/messages?limit=50&cursor=uuid
Authorization: Bearer <token>
```

**Query Parameters:**
- `limit` (optional): Number of messages to return (1-100, default: 50)
- `cursor` (optional): UUID for pagination (last message ID from previous request)

**Response:**
```json
{
  "messages": [
    {
      "id": "uuid",
      "content": "Hello, how are you?",
      "type": "text",
      "status": "sent",
      "sender": {
        "id": 1,
        "username": "user1",
        "level": 5
      },
      "isRead": false,
      "editedAt": null,
      "createdAt": "2024-01-15T10:30:00Z",
      "updatedAt": "2024-01-15T10:30:00Z"
    }
  ],
  "hasMore": true,
  "nextCursor": "uuid"
}
```

#### Send Message (HTTP fallback)
```http
POST /api/chats/:chatId/messages
Content-Type: application/json
Authorization: Bearer <token>

{
  "content": "Hello, how are you?",
  "type": "text"
}
```

**Response:** Single message object (same structure as above)

#### Mark Messages as Read
```http
PUT /api/chats/:chatId/read
Authorization: Bearer <token>
```

**Response:**
```json
{
  "markedCount": 5
}
```

#### Get Unread Count
```http
GET /api/chats/:chatId/unread-count
Authorization: Bearer <token>
```

**Response:**
```json
{
  "unreadCount": 3
}
```

### Error Responses

All endpoints return standardized error responses:

```json
{
  "statusCode": "400",
  "message": "Error description",
  "method": "POST",
  "timestamp": "2024-01-15T10:30:00Z",
  "path": "/api/chats"
}
```

**Common HTTP Status Codes:**
- `400` - Bad Request (validation errors, invalid data)
- `401` - Unauthorized (invalid/missing JWT token)
- `403` - Forbidden (not a chat participant)
- `404` - Not Found (chat doesn't exist)
- `429` - Too Many Requests (rate limit exceeded)

## 🔌 WebSocket Events

### Connection & Authentication

Connect to the chat namespace with JWT authentication:
```javascript
const socket = io('/chat', {
  auth: {
    token: 'your-jwt-token'  // JWT token without 'Bearer' prefix
  }
});

// Listen for connection confirmation
socket.on('connected', (data) => {
  console.log('Connected to chat system:', data);
  // User is automatically joined to all their chat rooms
});

// Handle connection errors
socket.on('connect_error', (error) => {
  console.error('Connection failed:', error.message);
  // Handle authentication failure or network issues
});
```

### Outgoing Events (Client → Server)

#### Join Chat Room (Optional)
```javascript
// Users are auto-joined to all their chats on connection
// Manual join is only needed for specific use cases
socket.emit('join_chat', { chatId: 'chat-uuid' });

// Listen for join confirmation
socket.on('joined_chat', (data) => {
  console.log('Joined chat:', data.chatId);
});
```

#### Leave Chat Room
```javascript
socket.emit('leave_chat', { chatId: 'chat-uuid' });

// Listen for leave confirmation
socket.on('left_chat', (data) => {
  console.log('Left chat:', data.chatId);
});
```

#### Send Message (Real-time)
```javascript
socket.emit('send_message', {
  chatId: 'chat-uuid',
  message: {
    content: 'Hello, how are you?',
    type: 'text'  // 'text' or 'system'
  }
});
```

#### Mark Messages as Read
```javascript
socket.emit('mark_read', { chatId: 'chat-uuid' });
```

#### Typing Indicators
```javascript
// Start typing
socket.emit('typing_start', { chatId: 'chat-uuid' });

// Stop typing
socket.emit('typing_stop', { chatId: 'chat-uuid' });
```

#### Connection Health Check
```javascript
// Send ping to check connection health
socket.emit('ping');

// Listen for pong response
socket.on('pong', (data) => {
  console.log('Connection healthy:', data.timestamp);
});
```

### Incoming Events (Server → Client)

#### New Message Received
```javascript
socket.on('new_message', (data) => {
  console.log('New message received:', data);
  /*
  data structure:
  {
    chatId: 'uuid',
    message: {
      id: 'uuid',
      content: 'Hello!',
      type: 'text',
      sender: {
        id: 1,
        username: 'user1',
        level: 5
      },
      createdAt: '2024-01-15T10:30:00Z',
      isRead: false  // Always false for new messages
    }
  }
  */
});
```

#### Messages Marked as Read
```javascript
socket.on('messages_read', (data) => {
  console.log('Messages marked as read:', data);
  /*
  data structure:
  {
    chatId: 'uuid',
    userId: 1,
    markedCount: 5
  }
  */
});
```

#### User Typing Status
```javascript
socket.on('user_typing', (data) => {
  console.log('Typing status:', data);
  /*
  data structure:
  {
    chatId: 'uuid',
    userId: 1,
    username: 'user1',
    isTyping: true  // true when started, false when stopped
  }
  */
});
```

#### Error Handling
```javascript
socket.on('error', (error) => {
  console.error('WebSocket error:', error);
  /*
  error structure:
  {
    message: 'Error description',
    event: 'send_message'  // The event that caused the error
  }
  */
});
```

### Rate Limiting

**WebSocket Rate Limits:**
- 10 messages per minute per user
- Exceeding the limit triggers a `WsException` with message "Rate limit exceeded. Please slow down."

**HTTP Rate Limits:**
- 30 messages per minute per user (for HTTP message sending)
- 3 requests per second (short-term)
- 20 requests per 10 seconds (medium-term)
- 100 requests per minute (long-term)

## 📊 TypeScript Interfaces

### Core Types

```typescript
// Chat Types
export enum ChatType {
  DIRECT = 'direct',
  GROUP = 'group'
}

export enum MessageType {
  TEXT = 'text',
  SYSTEM = 'system'
}

export enum MessageStatus {
  SENT = 'sent',
  DELIVERED = 'delivered',
  READ = 'read'
}

// User Interface
export interface User {
  id: number;
  username: string;
  level: number;
}

// Chat Interface
export interface Chat {
  id: string;
  type: ChatType;
  name?: string;
  description?: string;
  participants: User[];
  lastMessage?: Message;
  unreadCount: number;
  lastMessageAt?: string;
  createdAt: string;
  updatedAt: string;
}

// Message Interface
export interface Message {
  id: string;
  content: string;
  type: MessageType;
  status: MessageStatus;
  sender: User;
  isRead: boolean;
  editedAt?: string;
  createdAt: string;
  updatedAt: string;
}

// API Response Types
export interface PaginatedChatsResponse {
  chats: Chat[];
  hasMore: boolean;
  nextCursor?: string;
}

export interface PaginatedMessagesResponse {
  messages: Message[];
  hasMore: boolean;
  nextCursor?: string;
}

// WebSocket Event Types
export interface NewMessageEvent {
  chatId: string;
  message: Message;
}

export interface MessagesReadEvent {
  chatId: string;
  userId: number;
  markedCount: number;
}

export interface TypingEvent {
  chatId: string;
  userId: number;
  username: string;
  isTyping: boolean;
}

export interface ErrorEvent {
  message: string;
  event: string;
}

// Request DTOs
export interface CreateChatRequest {
  type: ChatType;
  participantIds: number[];
  name?: string;
  description?: string;
}

export interface SendMessageRequest {
  content: string;
  type: MessageType;
}
```

## 🔒 Authentication & Security

### JWT Token Structure
```typescript
interface JWTPayload {
  userId: number;
  username: string;
  iat: number;  // issued at
  exp: number;  // expires at
}
```

### Authentication Flow
1. **Login**: POST `/auth/login` with credentials
2. **Get Token**: Receive JWT token in response
3. **HTTP Requests**: Include `Authorization: Bearer <token>` header
4. **WebSocket**: Pass token in `auth.token` during connection
5. **Token Refresh**: Automatic refresh via cookie middleware

### Security Features
- **JWT Validation**: All requests validate JWT tokens
- **Authorization**: Users can only access chats they participate in
- **Input Sanitization**: All message content sanitized with DOMPurify
- **Rate Limiting**: Prevents spam and abuse
- **CORS**: Configured for specific origins
- **Validation**: Comprehensive input validation using class-validator

### Input Validation Rules
- **Message Content**: 1-2000 characters, required
- **Chat Name**: 1-100 characters (required for groups)
- **Chat Description**: Max 500 characters (optional)
- **Participant IDs**: 1-50 participants for groups
- **Pagination Limit**: 1-100 items per request

## 📱 Frontend Implementation Examples

### React Hook Implementation

```typescript
import { useEffect, useState, useCallback } from 'react';
import io, { Socket } from 'socket.io-client';
import { Chat, Message, NewMessageEvent, TypingEvent, MessagesReadEvent } from './types';

interface UseChatOptions {
  token: string;
  serverUrl?: string;
}

interface UseChatReturn {
  socket: Socket | null;
  isConnected: boolean;
  chats: Chat[];
  messages: Record<string, Message[]>;
  typingUsers: Record<string, Record<number, boolean>>;
  sendMessage: (chatId: string, content: string) => void;
  markAsRead: (chatId: string) => void;
  startTyping: (chatId: string) => void;
  stopTyping: (chatId: string) => void;
  loadMoreMessages: (chatId: string, cursor?: string) => Promise<void>;
  createChat: (type: 'direct' | 'group', participantIds: number[], name?: string) => Promise<Chat>;
}

export function useChat({ token, serverUrl = '' }: UseChatOptions): UseChatReturn {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [chats, setChats] = useState<Chat[]>([]);
  const [messages, setMessages] = useState<Record<string, Message[]>>({});
  const [typingUsers, setTypingUsers] = useState<Record<string, Record<number, boolean>>>({});

  // Initialize socket connection
  useEffect(() => {
    if (!token) return;

    const newSocket = io(`${serverUrl}/chat`, {
      auth: { token }
    });

    newSocket.on('connect', () => {
      setIsConnected(true);
      console.log('Connected to chat system');
    });

    newSocket.on('disconnect', () => {
      setIsConnected(false);
      console.log('Disconnected from chat system');
    });

    newSocket.on('connect_error', (error) => {
      console.error('Connection error:', error);
      setIsConnected(false);
    });

    // Handle incoming messages
    newSocket.on('new_message', (data: NewMessageEvent) => {
      setMessages(prev => ({
        ...prev,
        [data.chatId]: [...(prev[data.chatId] || []), data.message]
      }));

      // Update last message in chat list
      setChats(prev => prev.map(chat =>
        chat.id === data.chatId
          ? { ...chat, lastMessage: data.message, lastMessageAt: data.message.createdAt }
          : chat
      ));
    });

    // Handle typing indicators
    newSocket.on('user_typing', (data: TypingEvent) => {
      setTypingUsers(prev => ({
        ...prev,
        [data.chatId]: {
          ...prev[data.chatId],
          [data.userId]: data.isTyping
        }
      }));

      // Auto-clear typing indicator after 3 seconds
      if (data.isTyping) {
        setTimeout(() => {
          setTypingUsers(prev => ({
            ...prev,
            [data.chatId]: {
              ...prev[data.chatId],
              [data.userId]: false
            }
          }));
        }, 3000);
      }
    });

    // Handle read receipts
    newSocket.on('messages_read', (data: MessagesReadEvent) => {
      // Update read status for messages
      setMessages(prev => ({
        ...prev,
        [data.chatId]: prev[data.chatId]?.map(msg =>
          msg.sender.id !== data.userId ? { ...msg, isRead: true } : msg
        ) || []
      }));
    });

    newSocket.on('error', (error) => {
      console.error('WebSocket error:', error);
    });

    setSocket(newSocket);

    return () => {
      newSocket.close();
    };
  }, [token, serverUrl]);

  // Load initial chats
  useEffect(() => {
    if (!token) return;

    const loadChats = async () => {
      try {
        const response = await fetch('/api/chats', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        const data = await response.json();
        setChats(data.chats);
      } catch (error) {
        console.error('Failed to load chats:', error);
      }
    };

    loadChats();
  }, [token]);

  // Send message
  const sendMessage = useCallback((chatId: string, content: string) => {
    if (socket && isConnected) {
      socket.emit('send_message', {
        chatId,
        message: { content, type: 'text' }
      });
    }
  }, [socket, isConnected]);

  // Mark messages as read
  const markAsRead = useCallback((chatId: string) => {
    if (socket && isConnected) {
      socket.emit('mark_read', { chatId });
    }
  }, [socket, isConnected]);

  // Typing indicators
  const startTyping = useCallback((chatId: string) => {
    if (socket && isConnected) {
      socket.emit('typing_start', { chatId });
    }
  }, [socket, isConnected]);

  const stopTyping = useCallback((chatId: string) => {
    if (socket && isConnected) {
      socket.emit('typing_stop', { chatId });
    }
  }, [socket, isConnected]);

  // Load more messages with pagination
  const loadMoreMessages = useCallback(async (chatId: string, cursor?: string) => {
    try {
      const url = new URL(`/api/chats/${chatId}/messages`, window.location.origin);
      if (cursor) url.searchParams.set('cursor', cursor);

      const response = await fetch(url.toString(), {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const data = await response.json();

      setMessages(prev => ({
        ...prev,
        [chatId]: cursor
          ? [...data.messages, ...(prev[chatId] || [])]
          : data.messages
      }));
    } catch (error) {
      console.error('Failed to load messages:', error);
    }
  }, [token]);

  // Create new chat
  const createChat = useCallback(async (
    type: 'direct' | 'group',
    participantIds: number[],
    name?: string
  ): Promise<Chat> => {
    const response = await fetch('/api/chats', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        type,
        participantIds,
        name
      })
    });

    if (!response.ok) {
      throw new Error('Failed to create chat');
    }

    const newChat = await response.json();
    setChats(prev => [newChat, ...prev]);
    return newChat;
  }, [token]);

  return {
    socket,
    isConnected,
    chats,
    messages,
    typingUsers,
    sendMessage,
    markAsRead,
    startTyping,
    stopTyping,
    loadMoreMessages,
    createChat
  };
}
```

### Vue 3 Composition API Implementation

```typescript
// composables/useChat.ts
import { ref, reactive, onMounted, onUnmounted } from 'vue';
import io, { Socket } from 'socket.io-client';

export function useChat(token: string) {
  const socket = ref<Socket | null>(null);
  const isConnected = ref(false);
  const chats = ref([]);
  const messages = reactive<Record<string, any[]>>({});
  const typingUsers = reactive<Record<string, Record<number, boolean>>>({});

  const connect = () => {
    if (!token) return;

    socket.value = io('/chat', {
      auth: { token }
    });

    socket.value.on('connect', () => {
      isConnected.value = true;
    });

    socket.value.on('disconnect', () => {
      isConnected.value = false;
    });

    socket.value.on('new_message', (data) => {
      if (!messages[data.chatId]) {
        messages[data.chatId] = [];
      }
      messages[data.chatId].push(data.message);
    });

    socket.value.on('user_typing', (data) => {
      if (!typingUsers[data.chatId]) {
        typingUsers[data.chatId] = {};
      }
      typingUsers[data.chatId][data.userId] = data.isTyping;
    });
  };

  const sendMessage = (chatId: string, content: string) => {
    if (socket.value && isConnected.value) {
      socket.value.emit('send_message', {
        chatId,
        message: { content, type: 'text' }
      });
    }
  };

  const markAsRead = (chatId: string) => {
    if (socket.value && isConnected.value) {
      socket.value.emit('mark_read', { chatId });
    }
  };

  onMounted(() => {
    connect();
  });

  onUnmounted(() => {
    if (socket.value) {
      socket.value.close();
    }
  });

  return {
    socket,
    isConnected,
    chats,
    messages,
    typingUsers,
    sendMessage,
    markAsRead
  };
}
```

### Vanilla JavaScript Implementation

```javascript
class ChatClient {
  constructor(token, options = {}) {
    this.token = token;
    this.serverUrl = options.serverUrl || '';
    this.socket = null;
    this.isConnected = false;
    this.chats = [];
    this.messages = {};
    this.typingUsers = {};
    this.eventListeners = {};
  }

  connect() {
    this.socket = io(`${this.serverUrl}/chat`, {
      auth: { token: this.token }
    });

    this.socket.on('connect', () => {
      this.isConnected = true;
      this.emit('connected');
    });

    this.socket.on('disconnect', () => {
      this.isConnected = false;
      this.emit('disconnected');
    });

    this.socket.on('new_message', (data) => {
      if (!this.messages[data.chatId]) {
        this.messages[data.chatId] = [];
      }
      this.messages[data.chatId].push(data.message);
      this.emit('message', data);
    });

    this.socket.on('user_typing', (data) => {
      if (!this.typingUsers[data.chatId]) {
        this.typingUsers[data.chatId] = {};
      }
      this.typingUsers[data.chatId][data.userId] = data.isTyping;
      this.emit('typing', data);
    });

    this.socket.on('messages_read', (data) => {
      this.emit('messagesRead', data);
    });

    this.socket.on('error', (error) => {
      this.emit('error', error);
    });
  }

  disconnect() {
    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }
  }

  sendMessage(chatId, content) {
    if (this.socket && this.isConnected) {
      this.socket.emit('send_message', {
        chatId,
        message: { content, type: 'text' }
      });
    }
  }

  markAsRead(chatId) {
    if (this.socket && this.isConnected) {
      this.socket.emit('mark_read', { chatId });
    }
  }

  startTyping(chatId) {
    if (this.socket && this.isConnected) {
      this.socket.emit('typing_start', { chatId });
    }
  }

  stopTyping(chatId) {
    if (this.socket && this.isConnected) {
      this.socket.emit('typing_stop', { chatId });
    }
  }

  async loadChats() {
    try {
      const response = await fetch('/api/chats', {
        headers: {
          'Authorization': `Bearer ${this.token}`
        }
      });
      const data = await response.json();
      this.chats = data.chats;
      return data;
    } catch (error) {
      console.error('Failed to load chats:', error);
      throw error;
    }
  }

  async loadMessages(chatId, cursor) {
    try {
      const url = new URL(`/api/chats/${chatId}/messages`, window.location.origin);
      if (cursor) url.searchParams.set('cursor', cursor);

      const response = await fetch(url.toString(), {
        headers: {
          'Authorization': `Bearer ${this.token}`
        }
      });

      const data = await response.json();

      if (!this.messages[chatId]) {
        this.messages[chatId] = [];
      }

      if (cursor) {
        this.messages[chatId] = [...data.messages, ...this.messages[chatId]];
      } else {
        this.messages[chatId] = data.messages;
      }

      return data;
    } catch (error) {
      console.error('Failed to load messages:', error);
      throw error;
    }
  }

  async createChat(type, participantIds, name) {
    try {
      const response = await fetch('/api/chats', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.token}`
        },
        body: JSON.stringify({
          type,
          participantIds,
          name
        })
      });

      if (!response.ok) {
        throw new Error('Failed to create chat');
      }

      const newChat = await response.json();
      this.chats.unshift(newChat);
      return newChat;
    } catch (error) {
      console.error('Failed to create chat:', error);
      throw error;
    }
  }

  // Event system
  on(event, callback) {
    if (!this.eventListeners[event]) {
      this.eventListeners[event] = [];
    }
    this.eventListeners[event].push(callback);
  }

  off(event, callback) {
    if (this.eventListeners[event]) {
      this.eventListeners[event] = this.eventListeners[event].filter(cb => cb !== callback);
    }
  }

  emit(event, data) {
    if (this.eventListeners[event]) {
      this.eventListeners[event].forEach(callback => callback(data));
    }
  }
}

// Usage example
const chatClient = new ChatClient('your-jwt-token');

chatClient.on('connected', () => {
  console.log('Connected to chat system');
  chatClient.loadChats();
});

chatClient.on('message', (data) => {
  console.log('New message:', data);
});

chatClient.on('typing', (data) => {
  console.log('Typing status:', data);
});

chatClient.connect();
```

## 🔧 Connection Management & Reconnection

### Automatic Reconnection Strategy

```typescript
class ReconnectingChatClient {
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000; // Start with 1 second
  private maxReconnectDelay = 30000; // Max 30 seconds

  connect() {
    this.socket = io('/chat', {
      auth: { token: this.token },
      autoConnect: true,
      reconnection: true,
      reconnectionAttempts: this.maxReconnectAttempts,
      reconnectionDelay: this.reconnectDelay,
      reconnectionDelayMax: this.maxReconnectDelay,
      randomizationFactor: 0.5
    });

    this.socket.on('connect', () => {
      this.reconnectAttempts = 0;
      this.isConnected = true;
      console.log('Connected to chat system');
    });

    this.socket.on('disconnect', (reason) => {
      this.isConnected = false;
      console.log('Disconnected:', reason);

      if (reason === 'io server disconnect') {
        // Server disconnected the client, manual reconnection needed
        this.socket.connect();
      }
    });

    this.socket.on('reconnect', (attemptNumber) => {
      console.log('Reconnected after', attemptNumber, 'attempts');
      this.isConnected = true;
      this.reconnectAttempts = 0;
    });

    this.socket.on('reconnect_attempt', (attemptNumber) => {
      console.log('Reconnection attempt', attemptNumber);
      this.reconnectAttempts = attemptNumber;
    });

    this.socket.on('reconnect_failed', () => {
      console.error('Failed to reconnect after', this.maxReconnectAttempts, 'attempts');
      this.isConnected = false;
    });
  }

  // Heartbeat mechanism
  startHeartbeat() {
    setInterval(() => {
      if (this.socket && this.isConnected) {
        this.socket.emit('ping');
      }
    }, 30000); // Ping every 30 seconds
  }
}
```

## 🚨 Error Handling & Best Practices

### Comprehensive Error Handling

```typescript
class RobustChatClient {
  async sendMessageWithRetry(chatId: string, content: string, maxRetries = 3) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        if (this.socket && this.isConnected) {
          // Try WebSocket first
          this.socket.emit('send_message', {
            chatId,
            message: { content, type: 'text' }
          });
          return;
        } else {
          // Fallback to HTTP
          const response = await fetch(`/api/chats/${chatId}/messages`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${this.token}`
            },
            body: JSON.stringify({
              content,
              type: 'text'
            })
          });

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }

          return await response.json();
        }
      } catch (error) {
        console.error(`Send message attempt ${attempt} failed:`, error);

        if (attempt === maxRetries) {
          throw new Error(`Failed to send message after ${maxRetries} attempts: ${error.message}`);
        }

        // Exponential backoff
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
      }
    }
  }

  handleRateLimitError(error: any) {
    if (error.message.includes('Rate limit exceeded')) {
      // Show user-friendly message
      console.warn('Sending messages too quickly. Please slow down.');

      // Disable send button temporarily
      this.emit('rateLimited', {
        message: 'Please wait before sending another message',
        retryAfter: 60000 // 1 minute
      });

      setTimeout(() => {
        this.emit('rateLimitCleared');
      }, 60000);
    }
  }

  validateMessage(content: string): { valid: boolean; error?: string } {
    if (!content || content.trim().length === 0) {
      return { valid: false, error: 'Message cannot be empty' };
    }

    if (content.length > 2000) {
      return { valid: false, error: 'Message too long (max 2000 characters)' };
    }

    return { valid: true };
  }
}
```

### Performance Optimization Tips

1. **Message Pagination**: Always implement pagination for message loading
2. **Virtual Scrolling**: Use virtual scrolling for large message lists
3. **Debounced Typing**: Debounce typing indicators to reduce network traffic
4. **Message Caching**: Cache messages locally to reduce API calls
5. **Connection Health**: Implement heartbeat monitoring for connection reliability
6. **Memory Management**: Clean up event listeners and timers on component unmount

```typescript
// Debounced typing indicator
const debouncedTyping = debounce((chatId: string, isTyping: boolean) => {
  if (isTyping) {
    chatClient.startTyping(chatId);
  } else {
    chatClient.stopTyping(chatId);
  }
}, 300);

// Usage in input handler
const handleInputChange = (value: string, chatId: string) => {
  if (value.length > 0) {
    debouncedTyping(chatId, true);
  } else {
    debouncedTyping(chatId, false);
  }
};

// Connection health monitoring
const startHeartbeat = (socket) => {
  const heartbeatInterval = setInterval(() => {
    if (socket.connected) {
      socket.emit('ping');
    }
  }, 30000); // Ping every 30 seconds

  return heartbeatInterval;
};
```

## 📋 Production Checklist

### Before Going Live

- [ ] **Authentication**: Verify JWT token validation works correctly
- [ ] **Rate Limiting**: Test rate limiting behavior and user feedback
- [ ] **Error Handling**: Implement comprehensive error handling for all scenarios
- [ ] **Reconnection**: Test automatic reconnection after network issues
- [ ] **Performance**: Test with large message histories and multiple chats
- [ ] **Security**: Verify input sanitization and authorization checks
- [ ] **Monitoring**: Set up logging and monitoring for WebSocket connections
- [ ] **Fallbacks**: Ensure HTTP fallbacks work when WebSocket fails
- [ ] **Mobile**: Test on mobile devices and poor network conditions
- [ ] **Accessibility**: Ensure chat interface is accessible to all users
- [ ] **Database**: Run migration to add performance indexes
- [ ] **Memory**: Verify no memory leaks in WebSocket connections
- [ ] **Heartbeat**: Test connection health monitoring
- [ ] **Cleanup**: Verify proper cleanup on user disconnect

### Known Limitations

1. **Message Editing**: Not currently supported (messages are immutable)
2. **File Uploads**: Only text messages are supported
3. **Message Reactions**: Not implemented
4. **Group Chat Admin**: No admin/moderator roles for group chats
5. **Message Search**: No search functionality within chats
6. **Push Notifications**: Not implemented (WebSocket only)

### Future Enhancements

- Message editing and deletion
- File and image sharing
- Message reactions and emoji support
- Group chat administration
- Message search and filtering
- Push notifications for offline users
- Message encryption for enhanced security

---

## 📞 Support & Troubleshooting

### Common Issues

**Connection Issues:**
- Verify JWT token is valid and not expired
- Check network connectivity
- Ensure WebSocket connections are not blocked by firewall/proxy

**Message Not Sending:**
- Check rate limiting (10 messages/minute for WebSocket)
- Verify user is a participant in the chat
- Try HTTP fallback if WebSocket fails

**Performance Issues:**
- Implement message pagination
- Use virtual scrolling for large message lists
- Limit concurrent WebSocket connections

### Debug Mode

Enable debug logging for troubleshooting:

```javascript
localStorage.debug = 'socket.io-client:socket';
```

This comprehensive guide should provide everything needed to integrate with the chat system. For additional support or feature requests, please refer to the API documentation or contact the development team.
