// User interface to match backend UserBasicDto
export interface User {
  id: number;
  username: string;
  level: number;
  // Note: email and password are excluded in chat responses for security
}

// Extended user interface for full user data
export interface UserFull extends User {
  email: string;
  avatarUrl?: string;
  experience: number;
  energy: number;
  strength: number;
  endurance: number;
  intelligence: number;
  gold: number;
  money: number;
  aboutMe?: string;
  isPremium: boolean;
  premiumExpiresAt?: Date;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}
